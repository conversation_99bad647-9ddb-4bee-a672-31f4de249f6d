import React from 'react';
import PropTypes from 'prop-types';

// material-ui
import { useTheme } from '@mui/material/styles';
import { Box, Card, CardContent, Divider, Grid, Typography, LinearProgress, Chip, Avatar, Stack, Paper } from '@mui/material';

// material-ui icons
import {
  AccessTime as TimeIcon,
  Psychology as PredictionIcon,
  SentimentSatisfied as SatisfiedIcon,
  SentimentNeutral as NeutralIcon,
  SentimentDissatisfied as UnsatisfiedIcon,
  TrendingUp as ConfidenceIcon,
  Star as CommonIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';

// project imports
import MainCard from 'ui-component/cards/MainCard';
import SkeletonTotalGrowthBarChart from 'ui-component/cards/Skeleton/TotalGrowthBarChart';
import { gridSpacing } from 'store/constant';

export default function AnalyticsMetricsCard({ isLoading = false, data = null, error = null }) {
  const theme = useTheme();

  // Static data for demonstration
  const staticData = {
    session_duration: 245.5, // in seconds
    total_predictions: 1247,
    satisfied_count: 856,
    neutral_count: 234,
    unsatisfied_count: 157,
    average_confidence: 0.847, // 84.7%
    most_common_prediction: 'Produit Recommandé'
  };

  // Use provided data or fall back to static data
  const metrics = data || staticData;

  // Calculate percentages for sentiment distribution
  const totalSentiments = metrics.satisfied_count + metrics.neutral_count + metrics.unsatisfied_count;
  const satisfiedPercentage = totalSentiments > 0 ? (metrics.satisfied_count / totalSentiments) * 100 : 0;
  const neutralPercentage = totalSentiments > 0 ? (metrics.neutral_count / totalSentiments) * 100 : 0;
  const unsatisfiedPercentage = totalSentiments > 0 ? (metrics.unsatisfied_count / totalSentiments) * 100 : 0;

  // Format session duration
  const formatDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  // Format confidence percentage
  const formatConfidence = (confidence) => {
    return `${(confidence * 100).toFixed(1)}%`;
  };

  if (error) {
    return (
      <MainCard>
        <Grid container spacing={gridSpacing}>
          <Grid size={12}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '300px',
                backgroundColor: 'error.lighter',
                borderRadius: 2,
                border: 2,
                borderColor: 'error.main'
              }}
            >
              <Typography variant="h6" sx={{ color: 'error.main', mb: 1 }}>
                ❌ Erreur de chargement
              </Typography>
              <Typography variant="body2" sx={{ color: 'error.dark', textAlign: 'center' }}>
                Impossible de charger les données d'analyse: {error}
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </MainCard>
    );
  }

  if (isLoading) {
    return <SkeletonTotalGrowthBarChart />;
  }

  return (
    <MainCard>
      <Grid container spacing={gridSpacing}>
        {/* Header */}
        <Grid size={12}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <AnalyticsIcon sx={{ color: 'primary.main', mr: 2, fontSize: '2rem' }} />
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 0.5 }}>
                Métriques d'Analyse Prédictive
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Analyse des Sentiments
              </Typography>
            </Box>
          </Box>
        </Grid>

        {/* Top row - Key metrics */}
        <Grid size={12} sx={{ mb: 3 }}>
          <Grid container spacing={3}>
            {/* Session Duration */}
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Card
                sx={{
                  p: 3,
                  textAlign: 'center',
                  border: '1px solid',
                  borderColor: 'divider',
                  height: '140px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center'
                }}
              >
                <TimeIcon sx={{ color: 'primary.main', fontSize: '2rem', mb: 1 }} />
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Durée Session
                </Typography>
                <Typography variant="h5" sx={{ fontWeight: 600 }}>
                  {formatDuration(metrics.session_duration)}
                </Typography>
              </Card>
            </Grid>

            {/* Total Predictions */}
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Card
                sx={{
                  p: 3,
                  textAlign: 'center',
                  border: '1px solid',
                  borderColor: 'divider',
                  height: '140px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center'
                }}
              >
                <PredictionIcon sx={{ color: 'secondary.main', fontSize: '2rem', mb: 1 }} />
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Prédictions Totales
                </Typography>
                <Typography variant="h5" sx={{ fontWeight: 600 }}>
                  {metrics.total_predictions.toLocaleString()}
                </Typography>
              </Card>
            </Grid>

            {/* Average Confidence */}
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Card
                sx={{
                  p: 3,
                  textAlign: 'center',
                  border: '1px solid',
                  borderColor: 'divider',
                  height: '140px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center'
                }}
              >
                <ConfidenceIcon sx={{ color: 'success.main', fontSize: '2rem', mb: 1 }} />
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Confiance Moyenne
                </Typography>
                <Typography variant="h5" sx={{ fontWeight: 600 }}>
                  {formatConfidence(metrics.average_confidence)}
                </Typography>
              </Card>
            </Grid>

            {/* Most Common Prediction */}
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Card
                sx={{
                  p: 3,
                  textAlign: 'center',
                  border: '1px solid',
                  borderColor: 'divider',
                  height: '140px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center'
                }}
              >
                <CommonIcon sx={{ color: 'warning.main', fontSize: '2rem', mb: 1 }} />
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Prédiction Commune
                </Typography>
                <Typography variant="body1" sx={{ fontWeight: 600 }}>
                  {metrics.most_common_prediction}
                </Typography>
              </Card>
            </Grid>
          </Grid>
        </Grid>

        {/* Sentiment Analysis Section */}
        <Grid size={12}>
          {/* Section Header */}
          <Box sx={{ mb: 4 }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 700,
                color: 'text.primary',
                mb: 1,
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <Box
                sx={{
                  width: 4,
                  height: 24,
                  bgcolor: 'primary.main',
                  mr: 2,
                  borderRadius: 1
                }}
              />
              Analyse des Sentiments
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ ml: 3 }}>
              Distribution et métriques de satisfaction client
            </Typography>
          </Box>

          {/* Sentiment Cards Row */}
          <Grid container spacing={4} sx={{ mb: 4 }}>
            {/* Satisfied */}
            <Grid size={{ xs: 12, sm: 6, lg: 4 }}>
              <Card
                sx={{
                  p: 3,
                  border: '1px solid',
                  borderColor: 'divider',
                  bgcolor: 'background.paper',
                  height: '220px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  borderRadius: 2,
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
                    borderColor: 'success.main'
                  }
                }}
              >
                <Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar
                        sx={{
                          bgcolor: 'success.main',
                          width: 48,
                          height: 48,
                          mr: 2
                        }}
                      >
                        <SatisfiedIcon sx={{ color: 'white', fontSize: '1.5rem' }} />
                      </Avatar>
                      <Box>
                        <Typography variant="h6" sx={{ fontWeight: 700, color: 'text.primary' }}>
                          Satisfaits
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Clients contents
                        </Typography>
                      </Box>
                    </Box>
                    <Chip
                      label={`${satisfiedPercentage.toFixed(1)}%`}
                      sx={{
                        bgcolor: 'success.main',
                        color: 'white',
                        fontWeight: 700,
                        fontSize: '0.875rem',
                        height: 32,
                        borderRadius: 2
                      }}
                    />
                  </Box>
                  <Box sx={{ textAlign: 'center', mb: 3 }}>
                    <Typography variant="h2" sx={{ fontWeight: 800, color: 'success.main', mb: 1 }}>
                      {metrics.satisfied_count.toLocaleString()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                      sur {totalSentiments.toLocaleString()} évaluations
                    </Typography>
                  </Box>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={satisfiedPercentage}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'success.lighter',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'success.main',
                      borderRadius: 4
                    }
                  }}
                />
              </Card>
            </Grid>

            {/* Neutral */}
            <Grid size={{ xs: 12, sm: 6, lg: 4 }}>
              <Card
                sx={{
                  p: 3,
                  border: '1px solid',
                  borderColor: 'divider',
                  bgcolor: 'background.paper',
                  height: '220px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  borderRadius: 2,
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
                    borderColor: 'warning.main'
                  }
                }}
              >
                <Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar
                        sx={{
                          bgcolor: 'warning.main',
                          width: 48,
                          height: 48,
                          mr: 2
                        }}
                      >
                        <NeutralIcon sx={{ color: 'white', fontSize: '1.5rem' }} />
                      </Avatar>
                      <Box>
                        <Typography variant="h6" sx={{ fontWeight: 700, color: 'text.primary' }}>
                          Neutres
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Clients indécis
                        </Typography>
                      </Box>
                    </Box>
                    <Chip
                      label={`${neutralPercentage.toFixed(1)}%`}
                      sx={{
                        bgcolor: 'warning.main',
                        color: 'white',
                        fontWeight: 700,
                        fontSize: '0.875rem',
                        height: 32,
                        borderRadius: 2
                      }}
                    />
                  </Box>
                  <Box sx={{ textAlign: 'center', mb: 3 }}>
                    <Typography variant="h2" sx={{ fontWeight: 800, color: 'warning.main', mb: 1 }}>
                      {metrics.neutral_count.toLocaleString()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                      sur {totalSentiments.toLocaleString()} évaluations
                    </Typography>
                  </Box>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={neutralPercentage}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'warning.lighter',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'warning.main',
                      borderRadius: 4
                    }
                  }}
                />
              </Card>
            </Grid>

            {/* Unsatisfied */}
            <Grid size={{ xs: 12, sm: 6, lg: 4 }}>
              <Card
                sx={{
                  p: 3,
                  border: '1px solid',
                  borderColor: 'divider',
                  bgcolor: 'background.paper',
                  height: '220px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  borderRadius: 2,
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
                    borderColor: 'error.main'
                  }
                }}
              >
                <Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar
                        sx={{
                          bgcolor: 'error.main',
                          width: 48,
                          height: 48,
                          mr: 2
                        }}
                      >
                        <UnsatisfiedIcon sx={{ color: 'white', fontSize: '1.5rem' }} />
                      </Avatar>
                      <Box>
                        <Typography variant="h6" sx={{ fontWeight: 700, color: 'text.primary' }}>
                          Insatisfaits
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Clients mécontents
                        </Typography>
                      </Box>
                    </Box>
                    <Chip
                      label={`${unsatisfiedPercentage.toFixed(1)}%`}
                      sx={{
                        bgcolor: 'error.main',
                        color: 'white',
                        fontWeight: 700,
                        fontSize: '0.875rem',
                        height: 32,
                        borderRadius: 2
                      }}
                    />
                  </Box>
                  <Box sx={{ textAlign: 'center', mb: 3 }}>
                    <Typography variant="h2" sx={{ fontWeight: 800, color: 'error.main', mb: 1 }}>
                      {metrics.unsatisfied_count.toLocaleString()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                      sur {totalSentiments.toLocaleString()} évaluations
                    </Typography>
                  </Box>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={unsatisfiedPercentage}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'error.lighter',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'error.main',
                      borderRadius: 4
                    }
                  }}
                />
              </Card>
            </Grid>
          </Grid>

          {/* Summary Statistics Row */}
          <Grid container spacing={4}>
            <Grid size={{ xs: 12, md: 6 }}>
              <Card
                sx={{
                  p: 4,
                  textAlign: 'center',
                  border: '1px solid',
                  borderColor: 'divider',
                  bgcolor: 'background.paper',
                  height: '180px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  borderRadius: 2,
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
                    borderColor: 'success.main'
                  }
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                  <Avatar
                    sx={{
                      bgcolor: 'success.main',
                      width: 40,
                      height: 40,
                      mr: 2
                    }}
                  >
                    <SatisfiedIcon sx={{ color: 'white', fontSize: '1.25rem' }} />
                  </Avatar>
                  <Typography variant="h6" sx={{ fontWeight: 700, color: 'text.primary' }}>
                    Taux de Satisfaction Global
                  </Typography>
                </Box>
                <Typography variant="h1" sx={{ fontWeight: 800, color: 'success.main', mb: 1 }}>
                  {satisfiedPercentage.toFixed(1)}%
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                  Performance excellente
                </Typography>
              </Card>
            </Grid>

            <Grid size={{ xs: 12, md: 6 }}>
              <Card
                sx={{
                  p: 4,
                  textAlign: 'center',
                  border: '1px solid',
                  borderColor: 'divider',
                  bgcolor: 'background.paper',
                  height: '180px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  borderRadius: 2,
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
                    borderColor: 'primary.main'
                  }
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                  <Avatar
                    sx={{
                      bgcolor: 'primary.main',
                      width: 40,
                      height: 40,
                      mr: 2
                    }}
                  >
                    <ConfidenceIcon sx={{ color: 'white', fontSize: '1.25rem' }} />
                  </Avatar>
                  <Typography variant="h6" sx={{ fontWeight: 700, color: 'text.primary' }}>
                    Niveau de Confiance IA
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
                  <Typography variant="h2" sx={{ fontWeight: 800, color: 'primary.main', mr: 2 }}>
                    {formatConfidence(metrics.average_confidence)}
                  </Typography>
                  <Chip
                    label={metrics.average_confidence >= 0.8 ? 'Élevé' : metrics.average_confidence >= 0.6 ? 'Moyen' : 'Faible'}
                    color={metrics.average_confidence >= 0.8 ? 'success' : metrics.average_confidence >= 0.6 ? 'warning' : 'error'}
                    sx={{
                      fontWeight: 700,
                      fontSize: '0.875rem',
                      height: 32,
                      borderRadius: 2
                    }}
                  />
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                  Prédictions fiables
                </Typography>
              </Card>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </MainCard>
  );
}

AnalyticsMetricsCard.propTypes = {
  isLoading: PropTypes.bool,
  data: PropTypes.object,
  error: PropTypes.string
};
