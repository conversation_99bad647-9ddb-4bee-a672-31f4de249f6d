import React from 'react';
import PropTypes from 'prop-types';

// material-ui
import { useTheme } from '@mui/material/styles';
import { Box, Card, CardContent, Divider, Grid, Typography, LinearProgress, Chip, Avatar, Stack, Paper } from '@mui/material';

// material-ui icons
import {
  AccessTime as TimeIcon,
  Psychology as PredictionIcon,
  SentimentSatisfied as SatisfiedIcon,
  SentimentNeutral as NeutralIcon,
  SentimentDissatisfied as UnsatisfiedIcon,
  TrendingUp as ConfidenceIcon,
  Star as CommonIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';

// project imports
import MainCard from 'ui-component/cards/MainCard';
import SkeletonTotalGrowthBarChart from 'ui-component/cards/Skeleton/TotalGrowthBarChart';
import { gridSpacing } from 'store/constant';

export default function AnalyticsMetricsCard({ isLoading = false, data = null, error = null }) {
  const theme = useTheme();

  // Static data for demonstration
  const staticData = {
    session_duration: 245.5, // in seconds
    total_predictions: 1247,
    satisfied_count: 856,
    neutral_count: 234,
    unsatisfied_count: 157,
    average_confidence: 0.847, // 84.7%
    most_common_prediction: 'Produit Recommandé'
  };

  // Use provided data or fall back to static data
  const metrics = data || staticData;

  // Calculate percentages for sentiment distribution
  const totalSentiments = metrics.satisfied_count + metrics.neutral_count + metrics.unsatisfied_count;
  const satisfiedPercentage = totalSentiments > 0 ? (metrics.satisfied_count / totalSentiments) * 100 : 0;
  const neutralPercentage = totalSentiments > 0 ? (metrics.neutral_count / totalSentiments) * 100 : 0;
  const unsatisfiedPercentage = totalSentiments > 0 ? (metrics.unsatisfied_count / totalSentiments) * 100 : 0;

  // Format session duration
  const formatDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  // Format confidence percentage
  const formatConfidence = (confidence) => {
    return `${(confidence * 100).toFixed(1)}%`;
  };

  if (error) {
    return (
      <MainCard>
        <Grid container spacing={gridSpacing}>
          <Grid size={12}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '300px',
                backgroundColor: 'error.lighter',
                borderRadius: 2,
                border: 2,
                borderColor: 'error.main'
              }}
            >
              <Typography variant="h6" sx={{ color: 'error.main', mb: 1 }}>
                ❌ Erreur de chargement
              </Typography>
              <Typography variant="body2" sx={{ color: 'error.dark', textAlign: 'center' }}>
                Impossible de charger les données d'analyse: {error}
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </MainCard>
    );
  }

  if (isLoading) {
    return <SkeletonTotalGrowthBarChart />;
  }

  return (
    <MainCard>
      <Grid container spacing={gridSpacing}>
        {/* Header */}
        <Grid size={12}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <AnalyticsIcon sx={{ color: 'primary.main', mr: 2, fontSize: '2rem' }} />
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 0.5 }}>
                Métriques d'Analyse Prédictive
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Analyse des Sentiments
              </Typography>
            </Box>
          </Box>
        </Grid>

        {/* Top row - Key metrics */}
        <Grid size={12} sx={{ mb: 3 }}>
          <Grid container spacing={3}>
            {/* Session Duration */}
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Card
                sx={{
                  p: 3,
                  textAlign: 'center',
                  border: '1px solid',
                  borderColor: 'divider',
                  height: '140px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center'
                }}
              >
                <TimeIcon sx={{ color: 'primary.main', fontSize: '2rem', mb: 1 }} />
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Durée Session
                </Typography>
                <Typography variant="h5" sx={{ fontWeight: 600 }}>
                  {formatDuration(metrics.session_duration)}
                </Typography>
              </Card>
            </Grid>

            {/* Total Predictions */}
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Card
                sx={{
                  p: 3,
                  textAlign: 'center',
                  border: '1px solid',
                  borderColor: 'divider',
                  height: '140px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center'
                }}
              >
                <PredictionIcon sx={{ color: 'secondary.main', fontSize: '2rem', mb: 1 }} />
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Prédictions Totales
                </Typography>
                <Typography variant="h5" sx={{ fontWeight: 600 }}>
                  {metrics.total_predictions.toLocaleString()}
                </Typography>
              </Card>
            </Grid>

            {/* Average Confidence */}
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Card
                sx={{
                  p: 3,
                  textAlign: 'center',
                  border: '1px solid',
                  borderColor: 'divider',
                  height: '140px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center'
                }}
              >
                <ConfidenceIcon sx={{ color: 'success.main', fontSize: '2rem', mb: 1 }} />
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Confiance Moyenne
                </Typography>
                <Typography variant="h5" sx={{ fontWeight: 600 }}>
                  {formatConfidence(metrics.average_confidence)}
                </Typography>
              </Card>
            </Grid>

            {/* Most Common Prediction */}
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Card
                sx={{
                  p: 3,
                  textAlign: 'center',
                  border: '1px solid',
                  borderColor: 'divider',
                  height: '140px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center'
                }}
              >
                <CommonIcon sx={{ color: 'warning.main', fontSize: '2rem', mb: 1 }} />
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Prédiction Commune
                </Typography>
                <Typography variant="body1" sx={{ fontWeight: 600 }}>
                  {metrics.most_common_prediction}
                </Typography>
              </Card>
            </Grid>
          </Grid>
        </Grid>

        {/* Sentiment Analysis Section */}
        <Grid size={12} sx={{ mb: 3 }}>
          <Grid container spacing={5}>
            {/* Satisfied */}
            <Grid size={{ xs: 12, sm: 6, md: 4 }}>
              <Card
                sx={{
                  p: 3,
                  border: '1px solid',
                  borderColor: 'success.light',
                  bgcolor: 'success.lighter',
                  height: '180px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <SatisfiedIcon sx={{ color: 'success.main', mr: 1 }} />
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      Satisfaits
                    </Typography>
                  </Box>
                  <Chip
                    label={`${satisfiedPercentage.toFixed(1)}%`}
                    size="small"
                    sx={{ bgcolor: 'success.main', color: 'white', fontWeight: 600 }}
                  />
                </Box>
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 2, textAlign: 'center' }}>
                  {metrics.satisfied_count}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={satisfiedPercentage}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'success.lighter',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'success.main',
                      borderRadius: 4
                    }
                  }}
                />
              </Card>
            </Grid>

            {/* Neutral */}
            <Grid size={{ xs: 12, sm: 6, md: 4 }}>
              <Card
                sx={{
                  p: 3,
                  border: '1px solid',
                  borderColor: 'grey.300',
                  bgcolor: 'grey.50',
                  height: '180px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <NeutralIcon sx={{ color: 'grey.600', mr: 1 }} />
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      Neutres
                    </Typography>
                  </Box>
                  <Chip
                    label={`${neutralPercentage.toFixed(1)}%`}
                    size="small"
                    sx={{ bgcolor: 'grey.600', color: 'white', fontWeight: 600 }}
                  />
                </Box>
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 2, textAlign: 'center' }}>
                  {metrics.neutral_count}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={neutralPercentage}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'grey.200',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'grey.600',
                      borderRadius: 4
                    }
                  }}
                />
              </Card>
            </Grid>

            {/* Unsatisfied */}
            <Grid size={{ xs: 12, sm: 6, md: 4 }}>
              <Card
                sx={{
                  p: 3,
                  border: '1px solid',
                  borderColor: 'error.light',
                  bgcolor: 'error.lighter',
                  height: '180px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <UnsatisfiedIcon sx={{ color: 'error.main', mr: 1 }} />
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      Insatisfaits
                    </Typography>
                  </Box>
                  <Chip
                    label={`${unsatisfiedPercentage.toFixed(1)}%`}
                    size="small"
                    sx={{ bgcolor: 'error.main', color: 'white', fontWeight: 600 }}
                  />
                </Box>
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 2, textAlign: 'center' }}>
                  {metrics.unsatisfied_count}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={unsatisfiedPercentage}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'error.lighter',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'error.main',
                      borderRadius: 4
                    }
                  }}
                />
              </Card>
            </Grid>

          </Grid>

          {/* Summary Statistics Row */}
          <Grid container spacing={5} sx={{ mt: 2 }}>
            <Grid size={{ xs: 12, sm: 6 }}>
              <Card
                sx={{
                  p: 4,
                  textAlign: 'center',
                  border: '1px solid',
                  borderColor: 'success.light',
                  bgcolor: 'background.paper',
                  height: '160px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  borderRadius: 3,
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12)'
                  }
                }}
              >
                <Typography variant="body1" color="text.secondary" gutterBottom sx={{ fontWeight: 500 }}>
                  Taux de Satisfaction Global
                </Typography>
                <Typography
                  variant="h2"
                  sx={{
                    fontWeight: 800,
                    color: 'success.main',
                    mb: 1,
                    background: 'linear-gradient(45deg, #4caf50 30%, #66bb6a 90%)',
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent'
                  }}
                >
                  {satisfiedPercentage.toFixed(1)}%
                </Typography>
                <Typography variant="body2" color="success.main" sx={{ fontWeight: 600 }}>
                  Performance excellente
                </Typography>
              </Card>
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <Card
                sx={{
                  p: 4,
                  textAlign: 'center',
                  border: '1px solid',
                  borderColor: 'primary.light',
                  bgcolor: 'background.paper',
                  height: '160px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  borderRadius: 3,
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12)'
                  }
                }}
              >
                <Typography variant="body1" color="text.secondary" gutterBottom sx={{ fontWeight: 500 }}>
                  Niveau de Confiance IA
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
                  <Typography
                    variant="h3"
                    sx={{
                      fontWeight: 700,
                      color: 'primary.main',
                      mr: 2
                    }}
                  >
                    {formatConfidence(metrics.average_confidence)}
                  </Typography>
                  <Chip
                    label={metrics.average_confidence >= 0.8 ? 'Élevé' : metrics.average_confidence >= 0.6 ? 'Moyen' : 'Faible'}
                    color={metrics.average_confidence >= 0.8 ? 'success' : metrics.average_confidence >= 0.6 ? 'warning' : 'error'}
                    sx={{
                      fontWeight: 600,
                      fontSize: '0.875rem',
                      height: 32,
                      borderRadius: 2
                    }}
                  />
                </Box>
                <Typography variant="body2" color="primary.main" sx={{ fontWeight: 600 }}>
                  Prédictions fiables
                </Typography>
              </Card>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </MainCard>
  );
}

AnalyticsMetricsCard.propTypes = {
  isLoading: PropTypes.bool,
  data: PropTypes.object,
  error: PropTypes.string
};
